<template>
  <div class="panel-content">
    <div class="panel-header">
      <div class="panel-title">
        <span class="panel-icon">🌍</span>
        <span>世界观设定</span>
      </div>
      <div class="world-actions">
        <el-button size="small" type="primary" @click="addWorldSetting">
          <el-icon><Plus /></el-icon>
          新增
        </el-button>
        <el-button size="small" type="success" @click="openWorldGenerateDialog">
          🤖 AI生成
        </el-button>
      </div>
    </div>

    <div class="worldview-list" v-loading="worldSettingsLoading" element-loading-text="正在加载世界观...">
      <div v-for="setting in worldSettings" :key="setting.id" class="worldview-item">
        <div class="worldview-content" @click="editWorldSetting(setting)">
          <div class="worldview-header">
            <h4>{{ setting.title }}</h4>
            <el-tag :type="getWorldSettingTagType(setting.category)">{{ getWorldSettingTagText(setting.category) }}</el-tag>
          </div>
          <el-tooltip
            v-if="setting.description"
            :content="setting.description"
            placement="right"
            :disabled="setting.description.length <= 80"
            effect="light"
            :show-after="300"
          >
            <p class="worldview-description worldview-description-truncated">
              {{ setting.description.length > 80 ? setting.description.substring(0, 80) + '...' : setting.description }}
            </p>
          </el-tooltip>
          <p v-else class="worldview-description">暂无描述</p>
          <div class="worldview-meta">
            <span class="create-time">{{ formatDate(setting.createdAt) }}</span>
            <span v-if="setting.generated" class="ai-generated">AI生成</span>
          </div>
        </div>
        <div class="worldview-actions">
          <el-dropdown @command="(cmd) => handleWorldSettingAction(cmd, setting)" trigger="click">
            <el-button size="small" type="text" @click.stop>
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="edit">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-dropdown-item>
                <el-dropdown-item command="duplicate">
                  <el-icon><CopyDocument /></el-icon>
                  复制
                </el-dropdown-item>
                <el-dropdown-item command="delete" divided>
                  <el-icon><Delete /></el-icon>
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <div v-if="worldSettings.length === 0" class="empty-state">
        <p>暂无世界观设定</p>
        <el-button size="small" @click="addWorldSetting">创建第一个设定</el-button>
      </div>
    </div>

    <!-- 世界观编辑对话框 -->
    <el-dialog v-model="showWorldDialog" title="编辑世界观设定" width="600px">
      <el-form :model="worldForm" label-width="80px">
        <el-form-item label="设定标题">
          <el-input v-model="worldForm.title" />
        </el-form-item>
        <el-form-item label="类别">
          <el-select v-model="worldForm.category">
            <el-option label="世界设定" value="setting" />
            <el-option label="魔法体系" value="magic" />
            <el-option label="政治势力" value="politics" />
            <el-option label="地理环境" value="geography" />
            <el-option label="历史背景" value="history" />
          </el-select>
        </el-form-item>
        <el-form-item label="详细描述">
          <div class="form-item-with-ai">
            <el-input v-model="worldForm.description" type="textarea" :rows="6" />
            <el-button
              size="small"
              type="primary"
              @click="generateWorldSettingAI"
              :loading="isGeneratingWorldSetting"
              style="margin-top: 8px;"
            >
              <el-icon><Star /></el-icon>
              AI生成描述
            </el-button>
          </div>
        </el-form-item>
      </el-form>

      <!-- 流式生成状态显示 -->
      <div v-if="isStreaming && streamingType === 'worldSetting'" class="streaming-status-card">
        <div class="streaming-header">
          <span class="streaming-title">🤖 AI正在生成世界观设定...</span>
        </div>
        <div class="streaming-content-display" v-html="formatStreamingContent(streamingContent)"></div>
      </div>

      <template #footer>
        <el-button @click="showWorldDialog = false">取消</el-button>
        <el-button type="primary" @click="saveWorldSetting">保存</el-button>
      </template>
    </el-dialog>

    <!-- 世界观AI生成对话框 -->
    <el-dialog v-model="showWorldGenerateDialog" title="AI生成世界观设定" width="800px" @close="showWorldGenerateDialog = false">
      <div class="world-generate-content">
        <!-- 配置区域 -->
        <el-card v-if="!worldGenerating && generatedWorldSettings.length === 0" shadow="never" class="config-section">
          <template #header>
            <span>⚙️ 生成配置</span>
          </template>

          <el-form label-width="120px" size="default">
            <el-form-item label="生成数量">
              <el-input-number v-model="worldGenerateConfig.count" :min="1" :max="8" />
            </el-form-item>

            <el-form-item label="设定类型">
              <div class="world-type-options">
                <el-checkbox v-model="worldGenerateConfig.includeGeography">地理环境</el-checkbox>
                <el-checkbox v-model="worldGenerateConfig.includeCulture">文化社会</el-checkbox>
                <el-checkbox v-model="worldGenerateConfig.includeHistory">历史背景</el-checkbox>
                <el-checkbox v-model="worldGenerateConfig.includeMagic">魔法体系</el-checkbox>
                <el-checkbox v-model="worldGenerateConfig.includeTechnology">科技水平</el-checkbox>
                <el-checkbox v-model="worldGenerateConfig.includePolitics">政治势力</el-checkbox>
                <el-checkbox v-model="worldGenerateConfig.includeReligion">宗教信仰</el-checkbox>
                <el-checkbox v-model="worldGenerateConfig.includeEconomy">经济贸易</el-checkbox>
                <el-checkbox v-model="worldGenerateConfig.includeRaces">种族设定</el-checkbox>
                <el-checkbox v-model="worldGenerateConfig.includeLanguage">语言文字</el-checkbox>
              </div>
            </el-form-item>

            <!-- 提示词选择 -->
            <el-form-item label="使用提示词">
              <div style="display: flex; gap: 10px; align-items: center;">
                <el-button
                  type="primary"
                  plain
                  size="small"
                  @click="openWorldSettingPromptSelector"
                >
                  📝 选择提示词
                </el-button>
                <span v-if="worldSettingSelectedPrompt" class="selected-prompt-info">
                  已选择：{{ worldSettingSelectedPrompt.title }}
                </span>
                <el-button
                  v-if="worldSettingSelectedPrompt"
                  link
                  size="small"
                  type="danger"
                  @click="clearWorldSettingPrompt"
                >
                  清除
                </el-button>
              </div>
            </el-form-item>

            <el-form-item label="特殊要求">
              <el-input
                v-model="worldGenerateConfig.customPrompt"
                type="textarea"
                :rows="3"
                placeholder="例如：需要包含特定的种族设定、独特的政治制度、特殊的自然现象等..."
              />
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 流式生成区域 -->
        <el-card v-if="worldGenerating" shadow="never" class="streaming-section">
          <template #header>
            <span>🤖 AI正在生成世界观设定...</span>
          </template>

          <div class="streaming-content-container">
            <div class="streaming-content" v-html="formatStreamingContent(streamingContent)"></div>
          </div>
        </el-card>

        <!-- 生成结果区域 -->
        <el-card v-if="!worldGenerating && generatedWorldSettings.length > 0" shadow="never" class="results-section">
          <template #header>
            <div class="results-header">
              <span>✨ 生成结果 ({{ generatedWorldSettings.length }}个设定)</span>
              <div class="result-actions">
                <el-button size="small" @click="() => generatedWorldSettings.forEach(setting => setting.selected = true)">全选</el-button>
                <el-button size="small" @click="() => generatedWorldSettings.forEach(setting => setting.selected = false)">全不选</el-button>
              </div>
            </div>
          </template>

          <div class="generated-settings-list">
            <div
              v-for="setting in generatedWorldSettings"
              :key="setting.id"
              class="generated-setting-card"
              :class="{ selected: setting.selected !== false }"
              @click="toggleWorldSettingSelection(setting)"
            >
              <div class="setting-header">
                <div class="setting-basic-info">
                  <h4>{{ setting.title }}</h4>
                  <el-tag :type="getWorldSettingType(setting.type)" size="small">{{ setting.type }}</el-tag>
                </div>
                <div class="selection-indicator">
                  <el-icon v-if="setting.selected !== false" class="selected-icon"><Check /></el-icon>
                </div>
              </div>

              <div class="setting-content">
                <p>{{ setting.description || '暂无描述' }}</p>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showWorldGenerateDialog = false">取消</el-button>
          <el-button
            v-if="!worldGenerating && generatedWorldSettings.length === 0"
            type="primary"
            @click="generateWorldSettings"
            :disabled="!worldGenerateConfig.includeGeography && !worldGenerateConfig.includeCulture && !worldGenerateConfig.includeHistory && !worldGenerateConfig.includeMagic && !worldGenerateConfig.includeTechnology && !worldGenerateConfig.includePolitics && !worldGenerateConfig.includeReligion && !worldGenerateConfig.includeEconomy && !worldGenerateConfig.includeRaces && !worldGenerateConfig.includeLanguage"
          >
            🚀 开始生成
          </el-button>
          <el-button
            v-if="!worldGenerating && generatedWorldSettings.length > 0"
            @click="generateWorldSettings"
          >
            🔄 重新生成
          </el-button>
          <el-button
            v-if="!worldGenerating && generatedWorldSettings.length > 0"
            type="primary"
            @click="batchSaveWorldSettings"
          >
            ✅ 添加选中设定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 提示词选择对话框 -->
    <PromptSelectionDialog
      v-model:visible="showPromptDialog"
      :category="'worldview'"
      :available-prompts="availablePrompts"
      :is-streaming="false"
      :streaming-content="''"
      :show-streaming="false"
      @prompt-selected="handlePromptSelected"
      @copy-prompt="copyPromptToClipboard"
      @use-prompt="useSelectedPrompt"
      @go-to-library="goToPromptLibrary"
    />
  </div>
</template>

<script setup>
import { ref, computed, inject, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete, MoreFilled, CopyDocument, Check, Star } from '@element-plus/icons-vue'
import PromptSelectionDialog from '../PromptSelectionDialog.vue'

// 注入依赖
const novelStore = inject('novelStore')
const apiService = inject('apiService')
const currentNovel = inject('currentNovel')
const saveNovelData = inject('saveNovelData')
const checkApiAndBalance = inject('checkApiAndBalance')

// 导入API服务
import { worldSettingApi, promptApi } from '../../services/novelApi.js'

// Props
const props = defineProps({
  worldSettingsLoading: {
    type: Boolean,
    default: false
  }
})

// 数据
const worldSettings = computed(() => novelStore.worldSettings)
const showWorldDialog = ref(false)
const showWorldGenerateDialog = ref(false)
const isGeneratingWorldSetting = ref(false)
const worldGenerating = ref(false)
const generatedWorldSettings = ref([])

// 流式生成相关状态
const isStreaming = ref(false)
const streamingType = ref('')
const streamingContent = ref('')

// 提示词相关状态
const worldSettingSelectedPrompt = ref(null)
const worldSettingFinalPrompt = ref('')
const showPromptDialog = ref(false)
const availablePrompts = ref([])
const selectedPrompt = ref(null)
const promptVariables = ref({})
const finalPrompt = ref('')

const worldForm = ref({
  id: null,
  title: '',
  description: '',
  category: 'setting',
  details: ''
})

const worldGenerateConfig = ref({
  count: 3,
  includeGeography: true,
  includeCulture: true,
  includeHistory: true,
  includeMagic: false,
  includeTechnology: false,
  includePolitics: false,
  includeReligion: false,
  includeEconomy: false,
  includeRaces: false,
  includeLanguage: false,
  customPrompt: ''
})

// 方法
const getWorldSettingTagType = (category) => {
  const typeMap = {
    'setting': 'primary',
    'magic': 'danger',
    'politics': 'warning',
    'geography': 'success',
    'history': 'info'
  }
  return typeMap[category] || 'info'
}

const getWorldSettingTagText = (category) => {
  const textMap = {
    'setting': '世界设定',
    'magic': '魔法体系',
    'politics': '政治势力',
    'geography': '地理环境',
    'history': '历史背景'
  }
  return textMap[category] || category
}

const formatDate = (date) => {
  if (!date) return ''
  const d = new Date(date)
  return d.toLocaleDateString('zh-CN', {
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const addWorldSetting = () => {
  worldForm.value = {
    id: null,
    title: '',
    description: '',
    category: 'setting',
    details: ''
  }
  showWorldDialog.value = true
}

const editWorldSetting = (setting) => {
  worldForm.value = { ...setting }
  showWorldDialog.value = true
}

const deleteWorldSetting = (setting) => {
  ElMessageBox.confirm(`确定要删除设定《${setting.title}》吗？`, '确认删除', {
    type: 'warning'
  }).then(() => {
    novelStore.removeWorldSetting(setting.id)
    ElMessage.success('设定已删除')
    saveNovelData()
  }).catch(() => {})
}

const handleWorldSettingAction = (command, setting) => {
  switch (command) {
    case 'edit':
      editWorldSetting(setting)
      break
    case 'duplicate':
      duplicateWorldSetting(setting)
      break
    case 'delete':
      deleteWorldSetting(setting)
      break
  }
}

const duplicateWorldSetting = (setting) => {
  const newSetting = {
    ...setting,
    id: new Date().getTime(),
    title: setting.title + ' (副本)',
    createdAt: new Date(),
    generated: false
  }
  novelStore.addWorldSetting(newSetting)
  ElMessage.success('设定已复制')
  saveNovelData()
}

const saveWorldSetting = () => {
  if (!worldForm.value.title.trim()) {
    ElMessage.warning('请输入设定标题')
    return
  }
  
  if (worldForm.value.id) {
    // 编辑现有设定
    novelStore.removeWorldSetting(worldForm.value.id)
    novelStore.addWorldSetting(worldForm.value)
    ElMessage.success('设定信息已更新')
  } else {
    // 新增设定
    const newSetting = {
      ...worldForm.value,
      createdAt: new Date()
    }
    novelStore.addWorldSetting(newSetting)
    ElMessage.success('设定创建成功')
  }
  
  showWorldDialog.value = false
  saveNovelData()
}

const openWorldGenerateDialog = () => {
  showWorldGenerateDialog.value = true
  worldGenerateConfig.value = {
    count: 3,
    includeGeography: true,
    includeCulture: true,
    includeHistory: true,
    includeMagic: false,
    includeTechnology: false,
    includePolitics: false,
    includeReligion: false,
    includeEconomy: false,
    includeRaces: false,
    includeLanguage: false,
    customPrompt: ''
  }
  generatedWorldSettings.value = []
}

// AI生成单个世界观设定描述
const generateWorldSettingAI = async () => {
  if (!checkApiAndBalance()) return

  if (!worldForm.value.title?.trim()) {
    ElMessage.warning('请先输入设定标题')
    return
  }

  isGeneratingWorldSetting.value = true
  streamingContent.value = ''
  isStreaming.value = true
  streamingType.value = 'worldSetting'

  // 清空描述字段，准备接收生成内容
  worldForm.value.description = ''

  try {
    const categoryText = {
      'setting': '世界设定',
      'magic': '魔法体系',
      'politics': '政治势力',
      'geography': '地理环境',
      'history': '历史背景'
    }[worldForm.value.category] || '世界设定'

    const prompt = `=== 小说基本信息 ===
小说标题：${currentNovel.value?.title || '未命名小说'}
小说类型：${(() => {
      const genreMap = {
        'fantasy': '玄幻小说',
        'urban': '都市言情',
        'historical': '历史架空',
        'martial': '武侠修仙',
        'science': '科幻未来',
        'romance': '现代言情',
        'mystery': '悬疑推理',
        'adventure': '冒险奇幻',
        'horror': '恐怖惊悚',
        'general': '通用小说'
      }
      return genreMap[currentNovel.value?.genre] || '通用小说'
    })()}
小说简介：${currentNovel.value?.description || '暂无简介'}

=== 世界观设定生成任务 ===
请为上述小说生成世界观设定的详细描述。

=== 设定信息 ===
- 设定标题：${worldForm.value.title}
- 设定类别：${categoryText}

=== 生成要求 ===
请生成详细的设定描述，包括：
1. 具体的设定内容和规则
2. 在小说世界中的作用和意义
3. 与其他设定的关联性
4. 对故事情节的影响

要求描述详细、生动，符合小说的类型、风格和整体世界观。`

    const aiResponse = await apiService.generateTextStream(prompt, {
      maxTokens: null, // 移除token限制
      temperature: 0.8,
      type: 'worldview'
    }, (chunk, fullContent) => {
      // 实时更新流式内容
      streamingContent.value = fullContent

      // 实时更新表单字段
      worldForm.value.description = fullContent
    })

    // 最终更新
    worldForm.value.description = aiResponse

    ElMessage.success('AI世界观设定生成完成')
  } catch (error) {
    console.error('AI生成世界观设定失败:', error)
    ElMessage.error(`设定生成失败: ${error.message}`)
  } finally {
    isGeneratingWorldSetting.value = false
    isStreaming.value = false
    streamingContent.value = ''
  }
}

// AI生成世界观设定
const generateWorldSettings = async () => {
  if (!checkApiAndBalance()) return

  worldGenerating.value = true
  generatedWorldSettings.value = []
  streamingContent.value = ''
  isStreaming.value = true
  streamingType.value = 'worldSettings'

  try {
    let finalPrompt = ''

    // 如果用户选择了自定义提示词，使用自定义提示词
    if (worldSettingSelectedPrompt.value && worldSettingFinalPrompt.value) {
      finalPrompt = `=== 小说基本信息 ===
小说标题：${currentNovel.value?.title || '未命名小说'}
小说类型：${getChineseGenre(currentNovel.value?.genre)}
小说简介：${currentNovel.value?.description || '暂无简介'}

=== 世界观生成要求 ===
${worldSettingFinalPrompt.value}

请根据小说信息和以上要求生成${worldGenerateConfig.value.count}个世界观设定，确保设定符合小说的整体风格和世界观。`
      console.log('使用自定义世界观提示词:', finalPrompt)
    } else {
      // 使用默认的生成逻辑
      const includeTypes = []
      if (worldGenerateConfig.value.includeGeography) includeTypes.push('地理环境')
      if (worldGenerateConfig.value.includeCulture) includeTypes.push('文化社会')
      if (worldGenerateConfig.value.includeHistory) includeTypes.push('历史背景')
      if (worldGenerateConfig.value.includeMagic) includeTypes.push('魔法体系')
      if (worldGenerateConfig.value.includeTechnology) includeTypes.push('科技水平')
      if (worldGenerateConfig.value.includePolitics) includeTypes.push('政治势力')
      if (worldGenerateConfig.value.includeReligion) includeTypes.push('宗教信仰')
      if (worldGenerateConfig.value.includeEconomy) includeTypes.push('经济贸易')
      if (worldGenerateConfig.value.includeRaces) includeTypes.push('种族设定')
      if (worldGenerateConfig.value.includeLanguage) includeTypes.push('语言文字')

      finalPrompt = `=== 小说基本信息 ===
小说标题：${currentNovel.value?.title || '未命名小说'}
小说类型：${getChineseGenre(currentNovel.value?.genre)}
小说简介：${currentNovel.value?.description || '暂无简介'}

=== 世界观生成任务 ===
请为上述小说生成${worldGenerateConfig.value.count}个世界观设定。

=== 生成要求 ===
设定类型要求：${includeTypes.join('、')}
${worldGenerateConfig.value.customPrompt ? `特殊要求：${worldGenerateConfig.value.customPrompt}` : ''}

请为每个设定生成详细信息，格式如下：

设定1：
标题：[设定标题]
类型：[设定类型]
描述：[详细描述，包含具体的设定内容、规则、特点等]

设定2：
...

请确保所有设定都符合小说的类型、风格和世界观，设定之间具有关联性和一致性。`

      console.log('使用默认世界观提示词')
    }

    const aiResponse = await apiService.generateTextStream(finalPrompt, {
      maxTokens: null, // 移除token限制
      temperature: 0.8,
      type: 'worldview'
    }, (chunk, fullContent) => {
      // 实时更新流式内容
      streamingContent.value = fullContent

      // 实时解析世界观设定
      parseGeneratedWorldSettings(fullContent)

      // 自动滚动到最新内容
      nextTick(() => {
        const streamElement = document.querySelector('.streaming-content')
        if (streamElement) {
          streamElement.scrollTop = streamElement.scrollHeight
        }
      })
    })

    // 最终解析
    parseGeneratedWorldSettings(aiResponse)

    ElMessage.success(`成功生成 ${generatedWorldSettings.value.length} 个世界观设定`)
  } catch (error) {
    console.error('AI生成世界观设定失败:', error)
    ElMessage.error(`世界观生成失败: ${error.message}`)
  } finally {
    worldGenerating.value = false
    isStreaming.value = false
    streamingContent.value = ''
  }
}

// 解析生成的世界观设定
const parseGeneratedWorldSettings = (content) => {
  if (!content || !content.trim()) {
    generatedWorldSettings.value = []
    return
  }

  console.log('原始内容:', content)

  // 更灵活的分割方式，支持多种格式
  let settingBlocks = []

  // 尝试不同的分割模式
  if (content.includes('设定1：') || content.includes('设定2：')) {
    // 标准格式：设定1：、设定2：
    settingBlocks = content.split(/设定\d+[：:]/i).filter(block => block.trim())
  } else if (content.includes('## ') || content.includes('# ')) {
    // Markdown格式
    settingBlocks = content.split(/#{1,3}\s+/).filter(block => block.trim())
  } else if (content.includes('1.') || content.includes('2.')) {
    // 列表格式：1. 2. 3.
    settingBlocks = content.split(/\d+\./).filter(block => block.trim())
  } else if (content.includes('**') && content.includes('标题：')) {
    // 包含粗体标记的格式
    settingBlocks = content.split(/\*\*[^*]+\*\*/).filter(block => block.trim())
  } else {
    // 如果没有明确分割符，尝试按连续的"标题："分割
    if (content.split('标题：').length > 2) {
      settingBlocks = content.split('标题：').filter(block => block.trim())
      // 为每个块添加回标题标识符（除了第一个空块）
      settingBlocks = settingBlocks.map((block, index) => {
        if (index === 0 && !block.includes('标题：')) return null // 第一个通常是空的
        return block.includes('标题：') ? block : ('标题：' + block)
      }).filter(block => block !== null)
    } else {
      // 按双换行分割
      const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim())
      if (paragraphs.length > 1) {
        settingBlocks = paragraphs
      } else {
        // 单个大段落
        settingBlocks = [content]
      }
    }
  }

  console.log('分割后的块数:', settingBlocks.length)

  const parsed = []

  settingBlocks.forEach((block, index) => {
    if (!block.trim()) return

    console.log(`处理块 ${index}:`, block.substring(0, 100))

    const lines = block.split('\n').map(line => line.trim()).filter(line => line)
    const setting = {
      id: Date.now() + index * 1000,
      title: '',
      type: '其他',
      description: '',
      createdAt: new Date(),
      generated: true
    }

    let isInDescription = false
    let descriptionParts = []

    lines.forEach((line, lineIndex) => {
      if (line.startsWith('标题：') || line.startsWith('标题:')) {
        setting.title = line.replace(/标题[：:]/, '').trim()
        isInDescription = false
      } else if (line.startsWith('类型：') || line.startsWith('类型:')) {
        setting.type = line.replace(/类型[：:]/, '').trim()
        isInDescription = false
      } else if (line.startsWith('描述：') || line.startsWith('描述:')) {
        // 描述行可能包含内容
        const descriptionContent = line.replace(/描述[：:]/, '').trim()
        if (descriptionContent) {
          descriptionParts = [descriptionContent]
        } else {
          descriptionParts = []
        }
        isInDescription = true
      } else if (isInDescription && line && !line.match(/^(标题|类型|描述)[：:]/)) {
        // 描述的续行（不是其他字段的开始）
        descriptionParts.push(line)
      } else if (!setting.title && lineIndex === 0) {
        // 如果第一行没有"标题："前缀，直接作为标题
        setting.title = line.replace(/^[^\u4e00-\u9fa5a-zA-Z]*/, '').trim()
      } else if (!isInDescription && line && !line.match(/^(标题|类型|描述)[：:]/)) {
        // 如果还没有开始描述部分，且不是特定字段，将其作为描述
        descriptionParts.push(line)
        isInDescription = true
      }
    })

    // 组合描述内容
    if (descriptionParts.length > 0) {
      setting.description = descriptionParts.join('\n').trim()
    }

    // 如果仍然没有标题，尝试从内容中智能提取
    if (!setting.title) {
      if (setting.description && setting.description.length > 0) {
        const firstLine = setting.description.split('\n')[0]
        if (firstLine.length <= 50) {
          setting.title = firstLine
          setting.description = setting.description.split('\n').slice(1).join('\n').trim()
        } else {
          // 尝试从第一句话中提取关键词作为标题
          const firstSentence = firstLine.split(/[。！？.!?]/)[0]
          if (firstSentence.length <= 30) {
            setting.title = firstSentence
          } else {
            setting.title = `世界观设定${index + 1}`
          }
        }
      } else {
        setting.title = `世界观设定${index + 1}`
      }
    }

    // 如果解析失败，尝试将整个块作为一个设定处理
    if (!setting.title && !setting.description) {
      // 将整个块作为描述，从中提取标题
      const blockText = block.trim()
      if (blockText.length > 0) {
        const firstLine = blockText.split('\n')[0].trim()
        if (firstLine.length <= 50 && firstLine.length > 0) {
          setting.title = firstLine
          setting.description = blockText.split('\n').slice(1).join('\n').trim() || '详细设定内容'
        } else {
          setting.title = `世界观设定${index + 1}`
          setting.description = blockText
        }
      }
    }

    // 确保有描述内容
    if (!setting.description || setting.description.trim() === '') {
      setting.description = '暂无描述'
    }

    console.log(`解析结果 ${index}:`, {
      title: setting.title,
      type: setting.type,
      description: setting.description.substring(0, 100) + (setting.description.length > 100 ? '...' : '')
    })

    // 只要有标题就添加设定
    if (setting.title && setting.title.trim() !== '') {
      parsed.push(setting)
    }
  })

  // 如果没有解析到任何设定，但有内容，创建一个默认设定
  if (parsed.length === 0 && content.trim().length > 0) {
    const lines = content.trim().split('\n').filter(line => line.trim())
    if (lines.length > 0) {
      const defaultSetting = {
        id: Date.now(),
        title: lines[0].length <= 50 ? lines[0] : '世界观设定',
        type: '其他',
        description: lines.length > 1 ? lines.slice(1).join('\n') : lines[0],
        createdAt: new Date(),
        generated: true
      }
      parsed.push(defaultSetting)
      console.log('创建默认设定:', defaultSetting)
    }
  }

  console.log('最终解析结果数量:', parsed.length)
  generatedWorldSettings.value = parsed
}

// 切换世界观设定选择状态
const toggleWorldSettingSelection = (setting) => {
  setting.selected = setting.selected !== false ? false : true
}

// 批量保存世界观设定（确认添加生成的世界观设定）
const batchSaveWorldSettings = async () => {
  const selectedSettings = generatedWorldSettings.value.filter(setting => setting.selected !== false)

  if (selectedSettings.length === 0) {
    ElMessage.warning('请选择要添加的世界观设定')
    return
  }

  if (!currentNovel.value?.id) {
    ElMessage.error('小说信息不存在')
    return
  }

  try {
    // 保存世界观设定到后端并添加到store中
    const savedSettings = []
    for (let i = 0; i < selectedSettings.length; i++) {
      const setting = selectedSettings[i]
      try {
        const worldSettingData = {
          title: setting.title,
          description: setting.description,
          category: setting.category || 'setting',
          details: setting.details || ''
        }

        const savedWorldSetting = await worldSettingApi.createWorldSetting(currentNovel.value.id, worldSettingData)

        // 添加到store中
        novelStore.addWorldSetting({
          ...savedWorldSetting,
          createdAt: new Date(savedWorldSetting.createdAt),
          updatedAt: new Date(savedWorldSetting.updatedAt)
        })

        savedSettings.push(savedWorldSetting)
      } catch (error) {
        console.error(`保存世界观设定 ${setting.title} 失败:`, error)
        ElMessage.error(`保存世界观设定"${setting.title}"失败: ${error.message}`)
      }
    }

    // 保存数据到localStorage作为备份
    saveNovelData()

    // 关闭对话框
    showWorldGenerateDialog.value = false

    if (savedSettings.length > 0) {
      ElMessage.success(`成功添加 ${savedSettings.length} 个世界观设定`)
    }
  } catch (error) {
    console.error('批量保存世界观设定失败:', error)
    ElMessage.error('批量保存世界观设定失败: ' + (error.message || '未知错误'))
  }
}

// 获取世界观设定类型样式
const getWorldSettingType = (type) => {
  const typeMap = {
    '地理环境': 'success',
    '文化社会': 'primary',
    '历史背景': 'warning',
    '魔法体系': 'danger',
    '科技水平': 'info',
    '其他': ''
  }
  return typeMap[type] || ''
}

// 格式化流式内容
const formatStreamingContent = (content) => {
  if (!content) return ''

  // 将换行符转换为HTML换行
  let formatted = content.replace(/\n/g, '<br/>')
  // 高亮世界观设定标题
  formatted = formatted.replace(/(设定\d+：)/g, '<strong style="color: #409eff; font-size: 16px;">$1</strong>')
  return formatted
}

// 提示词相关方法
const openWorldSettingPromptSelector = async () => {
  try {
    // 加载提示词数据
    await loadPrompts()
    showPromptDialog.value = true
  } catch (error) {
    console.error('加载提示词失败:', error)
    ElMessage.error('加载提示词失败: ' + error.message)
  }
}

const clearWorldSettingPrompt = () => {
  worldSettingSelectedPrompt.value = null
  worldSettingFinalPrompt.value = ''
}

// 加载提示词数据
const loadPrompts = async () => {
  try {
    const data = await promptApi.getPrompts('worldview')
    availablePrompts.value = data || []
    console.log('世界观提示词加载完成，数量:', availablePrompts.value.length)
  } catch (error) {
    console.error('加载世界观提示词失败:', error)
    // 如果API调用失败，使用空数组
    availablePrompts.value = []
    throw error
  }
}

// 处理提示词选择事件
const handlePromptSelected = (data) => {
  selectedPrompt.value = data.prompt
  promptVariables.value = data.variables
  finalPrompt.value = data.finalPrompt
}

// 使用选中的提示词
const useSelectedPrompt = (data) => {
  if (!data || !data.prompt || !data.finalPrompt) {
    ElMessage.warning('请选择提示词并填充变量')
    return
  }

  worldSettingSelectedPrompt.value = data.prompt
  worldSettingFinalPrompt.value = data.finalPrompt
  showPromptDialog.value = false
  ElMessage.success('提示词已选择')
}

// 复制提示词到剪贴板
const copyPromptToClipboard = async (promptText) => {
  try {
    await navigator.clipboard.writeText(promptText)
    ElMessage.success('提示词已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 跳转到提示词库
const goToPromptLibrary = () => {
  // 通过emit事件通知父组件跳转
  // 或者直接使用router跳转（需要注入router）
  ElMessage.info('请前往提示词库添加世界观生成提示词')
}

// 获取中文类型名称
const getChineseGenre = (genre) => {
  const genreMap = {
    'fantasy': '玄幻小说',
    'urban': '都市言情',
    'historical': '历史架空',
    'martial': '武侠修仙',
    'science': '科幻未来',
    'romance': '现代言情',
    'mystery': '悬疑推理',
    'adventure': '冒险奇幻',
    'horror': '恐怖惊悚',
    'general': '通用小说'
  }
  return genreMap[genre] || '通用小说'
}
</script>

<style scoped>
.panel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  overflow: hidden;
  height: calc(100vh - 150px);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.panel-icon {
  font-size: 18px;
}

.world-actions {
  display: flex;
  gap: 8px;
}

.worldview-list {
  max-height: calc(100vh - 190px);
  overflow-y: auto;
}

.worldview-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.worldview-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.worldview-content {
  flex: 1;
  cursor: pointer;
}

.worldview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.worldview-header h4 {
  margin: 0;
  font-size: 14px;
  color: #303133;
  flex: 1;
  margin-right: 8px;
}

.worldview-description {
  margin: 6px 0;
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.worldview-description-truncated {
  cursor: help;
  transition: color 0.2s ease;
}

.worldview-description-truncated:hover {
  color: #303133;
}

.worldview-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 6px;
}

.worldview-meta .create-time {
  font-size: 12px;
  color: #909399;
}

.worldview-meta .ai-generated {
  font-size: 11px;
  color: #67c23a;
  background-color: #f0f9ff;
  padding: 2px 6px;
  border-radius: 10px;
  border: 1px solid #b3d8ff;
}

.worldview-actions {
  flex-shrink: 0;
  margin-left: 8px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.description-input-container {
  position: relative;
}

.ai-generate-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
}

/* 世界观生成对话框样式 */
.world-generate-content {
  max-height: 70vh;
  overflow-y: auto;
}

.world-type-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px 16px;
  margin-top: 8px;
  align-items: center;
}

.world-type-options .el-checkbox {
  margin: 0;
  white-space: nowrap;
  min-width: fit-content;
}

.generated-results {
  margin-top: 20px;
  border-top: 1px solid #e4e7ed;
  padding-top: 20px;
}

.generated-world-list {
  display: grid;
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.generated-world-card {
  position: relative;
  padding: 16px;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.generated-world-card:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.generated-world-card.selected {
  border-color: #409eff;
  background-color: #ecf5ff;
  box-shadow: 0 0 0 1px #409eff;
}

.world-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
}

.world-header h5 {
  margin: 0;
  font-size: 16px;
  color: #303133;
  flex: 1;
}

.world-description {
  margin: 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  max-height: 60px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.selection-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 流式生成状态显示 */
.streaming-status-card {
  margin: 16px 0;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.streaming-header {
  margin-bottom: 12px;
}

.streaming-title {
  font-weight: 600;
  color: #409eff;
}

.streaming-content-display {
  max-height: 200px;
  overflow-y: auto;
  padding: 8px;
  background-color: white;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.5;
}

/* 表单项与AI按钮 */
.form-item-with-ai {
  position: relative;
}

/* 配置区域样式 */
.config-section {
  margin-bottom: 16px;
}

/* 流式生成区域样式 */
.streaming-section {
  margin-bottom: 16px;
}

.streaming-content-container {
  max-height: 400px;
  overflow-y: auto;
}

.streaming-content {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
}

/* 结果区域样式 */
.results-section {
  margin-bottom: 16px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-actions {
  display: flex;
  gap: 8px;
}

.generated-settings-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.generated-setting-card {
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #ffffff;
}

.generated-setting-card:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.generated-setting-card.selected {
  border-color: #409eff;
  background-color: #ecf5ff;
  box-shadow: 0 0 0 1px #409eff;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.setting-basic-info {
  flex: 1;
}

.setting-basic-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #303133;
}

.setting-content p {
  margin: 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  max-height: 60px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.selection-indicator {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selected-icon {
  color: #409eff;
}

.selected-prompt-info {
  font-size: 14px;
  color: #606266;
}
</style>
