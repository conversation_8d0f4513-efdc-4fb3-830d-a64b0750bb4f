<template>
  <div class="character-management-panel">
    <!-- 人物管理面板 -->
    <div class="panel-content">
      <div class="panel-header">
        <div class="panel-title">
          <span class="panel-icon">👥</span>
          <span>人物角色</span>
        </div>
        <div class="character-actions">
          <el-button size="small" type="primary" @click="addCharacter">
            <el-icon><Plus /></el-icon>
            新增
          </el-button>
          <el-button size="small" type="success" @click="showBatchGenerateDialog">
            🤖 AI批量生成
          </el-button>
        </div>
      </div>

      <div class="characters-list" v-loading="charactersLoading" element-loading-text="正在加载人物...">
        <div v-for="character in characters" :key="character.id" class="character-item">
          <div class="character-content" @click="editCharacter(character)">
            <div class="character-avatar">
              <img v-if="character.avatar" :src="character.avatar" />
              <div v-else class="default-avatar">{{ character.name?.charAt(0) || '？' }}</div>
            </div>
            <div class="character-info">
              <h4>{{ character.name }}</h4>
              <div class="character-meta">
                <el-tag :type="getRoleType(character.role)" size="small">{{ getRoleText(character.role) }}</el-tag>
                <el-tag v-if="character.gender" type="info" size="small">{{ getGenderText(character.gender) }}</el-tag>
                <span v-if="character.age" class="age-text">{{ character.age }}岁</span>
              </div>
              <el-tooltip
                v-if="character.personality"
                :content="character.personality"
                placement="right"
                :disabled="character.personality.length <= 60"
                effect="light"
                :show-after="300"
              >
                <p class="character-desc character-desc-truncated">
                  {{ character.personality.length > 60 ? character.personality.substring(0, 60) + '...' : character.personality }}
                </p>
              </el-tooltip>
              <div class="character-tags" v-if="character.tags && character.tags.length">
                <el-tag v-for="tag in character.tags" :key="tag" size="small">{{ tag }}</el-tag>
              </div>
            </div>
          </div>
          <div class="character-actions">
            <el-dropdown @command="(cmd) => handleCharacterAction(cmd, character)" trigger="click">
              <el-button size="small" type="text" @click.stop>
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" divided>
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <div v-if="characters.length === 0" class="empty-state">
          <p>暂无人物设定</p>
          <el-button size="small" @click="addCharacter">创建第一个角色</el-button>
        </div>
      </div>
    </div>

    <!-- 人物编辑对话框 -->
    <el-dialog v-model="showCharacterDialog" title="编辑角色" width="700px">
      <el-form :model="characterForm" label-width="80px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名">
              <el-input v-model="characterForm.name" />
            </el-form-item>
            <el-form-item label="角色">
              <el-select v-model="characterForm.role">
                <el-option label="主角" value="protagonist" />
                <el-option label="配角" value="supporting" />
                <el-option label="反派" value="antagonist" />
                <el-option label="次要角色" value="minor" />
              </el-select>
            </el-form-item>
            <el-form-item label="性别">
              <el-radio-group v-model="characterForm.gender">
                <el-radio label="male">男</el-radio>
                <el-radio label="female">女</el-radio>
                <el-radio label="other">其他</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="年龄">
              <el-input-number v-model="characterForm.age" :min="0" :max="1000" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="外貌">
              <el-input v-model="characterForm.appearance" type="textarea" :rows="3" />
            </el-form-item>
            <el-form-item label="性格">
              <el-input v-model="characterForm.personality" type="textarea" :rows="3" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="背景故事">
          <div class="form-item-with-ai">
            <el-input v-model="characterForm.background" type="textarea" :rows="4" />
            <div class="ai-button-group" style="margin-top: 8px;">
              <el-button
                size="small"
                type="primary"
                @click="generateCharacterAI"
                style="flex: 1;"
              >
                <el-icon><Star /></el-icon>
                AI生成角色信息
              </el-button>
              <el-button size="small" @click="openPromptDialog('character')" style="margin-left: 8px;">
                📝 提示词
              </el-button>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="标签">
          <el-input v-model="characterTagInput" placeholder="输入标签后按回车" @keyup.enter="addCharacterTag">
            <template #append>
              <el-button @click="addCharacterTag">添加</el-button>
            </template>
          </el-input>
          <div v-if="characterForm.tags.length > 0" style="margin-top: 8px;">
            <el-tag
              v-for="(tag, index) in characterForm.tags"
              :key="index"
              closable
              @close="removeCharacterTag(index)"
              style="margin-right: 8px;"
            >
              {{ tag }}
            </el-tag>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCharacterDialog = false">取消</el-button>
        <el-button type="primary" @click="saveCharacter">保存</el-button>
      </template>
    </el-dialog>

    <!-- 批量生成角色对话框 -->
    <el-dialog v-model="showBatchGenerateCharacterDialog" title="AI批量生成角色" width="900px" @close="closeBatchGenerateDialog">
      <div class="batch-generate-content">
        <!-- 配置区域 -->
        <el-card v-if="!batchGenerating && generatedCharacters.length === 0" shadow="never" class="config-section">
          <template #header>
            <span>⚙️ 生成配置</span>
          </template>

          <el-form label-width="120px" size="default">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="生成数量">
                  <el-input-number v-model="batchGenerateConfig.count" :min="2" :max="10" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="角色类型">
                  <div class="character-type-options">
                    <el-checkbox v-model="batchGenerateConfig.includeMainCharacters">主角</el-checkbox>
                    <el-checkbox v-model="batchGenerateConfig.includeSupportingCharacters">配角</el-checkbox>
                    <el-checkbox v-model="batchGenerateConfig.includeMinorCharacters">次要角色</el-checkbox>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="特殊要求">
              <el-input
                v-model="batchGenerateConfig.customPrompt"
                type="textarea"
                :rows="3"
                placeholder="例如：需要包含反派角色、特定职业角色、具有魔法能力的角色等..."
              />
            </el-form-item>

            <el-form-item label="智能分配">
              <el-checkbox v-model="batchGenerateConfig.autoAssignRoles">自动平衡角色关系和重要性</el-checkbox>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 流式生成区域 -->
        <el-card v-if="batchGenerating" shadow="never" class="streaming-section">
          <template #header>
            <span>🤖 AI正在生成角色...</span>
          </template>

          <div class="streaming-content-container">
            <div class="streaming-content" v-html="formatStreamingContent(streamingContent)"></div>
          </div>
        </el-card>

        <!-- 生成结果区域 -->
        <el-card v-if="!batchGenerating && generatedCharacters.length > 0" shadow="never" class="results-section">
          <template #header>
            <div class="results-header">
              <span>✨ 生成结果 ({{ generatedCharacters.length }}个角色)</span>
              <div class="result-actions">
                <el-button size="small" @click="() => generatedCharacters.forEach(char => char.selected = true)">全选</el-button>
                <el-button size="small" @click="() => generatedCharacters.forEach(char => char.selected = false)">全不选</el-button>
              </div>
            </div>
          </template>

          <div class="generated-characters-grid">
            <div
              v-for="character in generatedCharacters"
              :key="character.id"
              class="generated-character-card"
              :class="{ selected: character.selected !== false }"
              @click="toggleCharacterSelection(character)"
            >
              <div class="character-header">
                <div class="character-avatar-preview">
                  <div class="default-avatar">{{ character.name?.charAt(0) || '？' }}</div>
                </div>
                <div class="character-basic-info">
                  <h4>{{ character.name }}</h4>
                  <div class="character-meta">
                    <el-tag :type="getRoleType(character.role)" size="small">{{ getRoleText(character.role) }}</el-tag>
                    <el-tag type="info" size="small">{{ getGenderText(character.gender) }}</el-tag>
                    <span class="age-text">{{ character.age }}岁</span>
                  </div>
                </div>
                <div class="selection-indicator">
                  <el-icon v-if="character.selected !== false" class="selected-icon"><Check /></el-icon>
                </div>
              </div>

              <div class="character-details">
                <div class="detail-item">
                  <label>外貌：</label>
                  <p>{{ character.appearance || '暂无描述' }}</p>
                </div>
                <div class="detail-item">
                  <label>性格：</label>
                  <p>{{ character.personality || '暂无描述' }}</p>
                </div>
                <div class="detail-item">
                  <label>背景：</label>
                  <p>{{ character.background || '暂无描述' }}</p>
                </div>
                <div class="character-tags-preview" v-if="character.tags?.length">
                  <el-tag v-for="tag in character.tags" :key="tag" size="small">{{ tag }}</el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeBatchGenerateDialog">取消</el-button>
          <el-button
            v-if="!batchGenerating && generatedCharacters.length === 0"
            type="primary"
            @click="batchGenerateCharacters"
            :disabled="!batchGenerateConfig.includeMainCharacters && !batchGenerateConfig.includeSupportingCharacters && !batchGenerateConfig.includeMinorCharacters"
          >
            🚀 开始生成
          </el-button>

          <el-button
            v-if="!batchGenerating && generatedCharacters.length > 0"
            @click="batchGenerateCharacters"
          >
            🔄 重新生成
          </el-button>
          <el-button
            v-if="!batchGenerating && generatedCharacters.length > 0"
            type="primary"
            @click="confirmAddGeneratedCharacters"
          >
            ✅ 添加选中角色
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 提示词选择对话框 -->
    <PromptSelectionDialog
      v-model:visible="showPromptDialog"
      :category="'character'"
      :available-prompts="availablePrompts"
      :is-streaming="false"
      :streaming-content="''"
      :show-streaming="false"
      @prompt-selected="handlePromptSelected"
      @copy-prompt="copyPromptToClipboard"
      @use-prompt="useSelectedPrompt"
      @go-to-library="goToPromptLibrary"
    />
  </div>
</template>

<script setup>
import { ref, computed, inject, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, MoreFilled, Edit, Delete, Star, Check } from '@element-plus/icons-vue'
import { characterApi, promptApi } from '../../services/novelApi.js'
import PromptSelectionDialog from '../PromptSelectionDialog.vue'

// 注入依赖
const currentNovel = inject('currentNovel')
const checkApiAndBalance = inject('checkApiAndBalance')
const apiService = inject('apiService')
const saveNovelData = inject('saveNovelData')

// Props
const props = defineProps({
  characters: {
    type: Array,
    required: true
  },
  charactersLoading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'update:characters',
  'update:charactersLoading',
  'open-prompt-dialog'
])

// 响应式数据
const showCharacterDialog = ref(false)
const characterForm = ref({
  id: null,
  name: '',
  role: 'supporting',
  gender: 'male',
  age: 25,
  appearance: '',
  personality: '',
  background: '',
  tags: [],
  avatar: ''
})
const characterTagInput = ref('')

// 批量生成相关数据
const showBatchGenerateCharacterDialog = ref(false)
const batchGenerateConfig = ref({
  count: 5,
  includeMainCharacters: true,
  includeSupportingCharacters: true,
  includeMinorCharacters: true,
  customPrompt: '',
  autoAssignRoles: true
})
const batchGenerating = ref(false)
const generatedCharacters = ref([])
const streamingContent = ref('')

// 提示词相关数据
const showPromptDialog = ref(false)
const availablePrompts = ref([])
const selectedPrompt = ref(null)
const promptVariables = ref({})
const finalPrompt = ref('')

// 计算属性
const characters = computed({
  get: () => props.characters,
  set: (value) => emit('update:characters', value)
})

const charactersLoading = computed({
  get: () => props.charactersLoading,
  set: (value) => emit('update:charactersLoading', value)
})

// 工具方法
const getRoleType = (role) => {
  const roleMap = {
    'protagonist': 'danger',
    'supporting': 'primary',
    'antagonist': 'warning',
    'minor': 'info'
  }
  return roleMap[role] || 'info'
}

const getRoleText = (role) => {
  const roleMap = {
    'protagonist': '主角',
    'supporting': '配角',
    'antagonist': '反派',
    'minor': '次要角色'
  }
  return roleMap[role] || '配角'
}

const getGenderText = (gender) => {
  const genderMap = {
    'male': '男',
    'female': '女',
    'other': '其他'
  }
  return genderMap[gender] || '男'
}

// 人物管理方法
const addCharacter = () => {
  characterForm.value = {
    id: null,
    name: '',
    role: 'supporting',
    gender: 'male',
    age: 25,
    appearance: '',
    personality: '',
    background: '',
    tags: [],
    avatar: ''
  }
  showCharacterDialog.value = true
}

const editCharacter = (character) => {
  characterForm.value = { ...character }
  showCharacterDialog.value = true
}

const saveCharacter = async () => {
  if (!characterForm.value.name.trim()) {
    ElMessage.warning('请输入角色姓名')
    return
  }

  if (!currentNovel.value?.id) {
    ElMessage.error('小说信息不存在')
    return
  }

  try {
    if (characterForm.value.id) {
      // 编辑现有角色
      const characterData = {
        name: characterForm.value.name,
        role: characterForm.value.role,
        gender: characterForm.value.gender,
        age: characterForm.value.age,
        appearance: characterForm.value.appearance,
        personality: characterForm.value.personality,
        background: characterForm.value.background,
        tags: characterForm.value.tags,
        avatar: characterForm.value.avatar
      }

      const updatedCharacter = await characterApi.updateCharacter(
        currentNovel.value.id,
        characterForm.value.id,
        characterData
      )

      // 更新本地数据
      const index = characters.value.findIndex(c => c.id === characterForm.value.id)
      if (index > -1) {
        characters.value[index] = {
          ...updatedCharacter,
          createdAt: new Date(updatedCharacter.createdAt),
          updatedAt: new Date(updatedCharacter.updatedAt)
        }
      }
      ElMessage.success('角色信息已更新')
    } else {
      // 新增角色
      const characterData = {
        name: characterForm.value.name,
        role: characterForm.value.role,
        gender: characterForm.value.gender,
        age: characterForm.value.age,
        appearance: characterForm.value.appearance,
        personality: characterForm.value.personality,
        background: characterForm.value.background,
        tags: characterForm.value.tags,
        avatar: characterForm.value.avatar
      }

      const newCharacter = await characterApi.createCharacter(currentNovel.value.id, characterData)

      // 添加到本地角色列表
      characters.value.push({
        ...newCharacter,
        createdAt: new Date(newCharacter.createdAt),
        updatedAt: new Date(newCharacter.updatedAt)
      })

      ElMessage.success('角色创建成功')
    }
  } catch (error) {
    console.error('保存角色失败:', error)
    ElMessage.error('保存角色失败: ' + (error.message || '未知错误'))
    return
  }

  showCharacterDialog.value = false
  saveNovelData() // 同时保存到localStorage作为备份
}

// AI生成角色
const generateCharacterAI = async () => {
  if (!checkApiAndBalance()) return

  if (!characterForm.value.name.trim()) {
    ElMessage.warning('请先输入角色姓名')
    return
  }

  // 设置流式生成状态
  const isStreaming = ref(true)
  const streamingContent = ref('')

  // 清空表单相关字段，准备接收生成内容
  characterForm.value.appearance = ''
  characterForm.value.personality = ''
  characterForm.value.background = ''
  characterForm.value.tags = []

  try {
    const prompt = `=== 小说基本信息 ===
小说标题：${currentNovel.value?.title || '未命名小说'}
小说类型：${(() => {
  const genreMap = {
    'fantasy': '玄幻',
    'romance': '言情',
    'urban': '都市',
    'historical': '历史',
    'scifi': '科幻',
    'mystery': '悬疑',
    'martial': '武侠',
    'game': '游戏',
    'military': '军事',
    'sports': '体育',
    'other': '其他'
  }
  return genreMap[currentNovel.value?.genre] || '现代'
})()}
小说简介：${currentNovel.value?.description || '暂无简介'}

=== 角色生成任务 ===
你是一个专业的角色生成器。请为上述小说中的角色《${characterForm.value.name}》生成详细信息。

【重要】必须严格按照以下格式输出，不要添加任何额外的解释或文字：

外貌：身高一米七五，黑发黑眼，面容清秀
性格：温和友善，聪明机智，有时略显内向
背景：出身书香门第，自幼受到良好教育，立志成为学者
标签：知识分子,温和,聪慧

请完全按照以上示例格式生成角色信息，必须包含：外貌、性格、背景、标签这4个字段。

=== 角色基本设定 ===
- 姓名：${characterForm.value.name}
- 角色定位：${characterForm.value.role === 'protagonist' ? '主角' : characterForm.value.role === 'antagonist' ? '反派' : '配角'}
- 性别：${characterForm.value.gender === 'male' ? '男' : characterForm.value.gender === 'female' ? '女' : '其他'}
- 年龄：${characterForm.value.age}岁

请确保角色设定符合小说的世界观、类型和风格特点。

开始生成：`

    // 为单个角色生成添加强制格式后缀
    const singleCharacterFormatSuffix = `

=== 重要格式要求 ===
无论上述提示词如何，你必须严格按照以下格式输出，不得有任何偏差：

外貌：[详细外貌描述]
性格：[性格特点描述]
背景：[背景故事描述]
标签：[标签1,标签2,标签3]

必须包含这4个字段，每个字段占一行。`

    const promptWithFormat = prompt + singleCharacterFormatSuffix

    const aiResponse = await apiService.generateTextStream(promptWithFormat, {
      maxTokens: null,
      temperature: 0.8,
      type: 'character'
    }, (chunk, fullContent) => {
      streamingContent.value = fullContent

      // 实时解析并填充表单
      const lines = fullContent.split('\n')
      for (const line of lines) {
        const trimmed = line.trim()
        if (trimmed.startsWith('外貌：')) {
          characterForm.value.appearance = trimmed.replace('外貌：', '').trim()
        } else if (trimmed.startsWith('性格：')) {
          characterForm.value.personality = trimmed.replace('性格：', '').trim()
        } else if (trimmed.startsWith('背景：')) {
          characterForm.value.background = trimmed.replace('背景：', '').trim()
        } else if (trimmed.startsWith('标签：')) {
          const tagString = trimmed.replace('标签：', '').trim()
          if (tagString) {
            characterForm.value.tags = tagString.split(',').map(tag => tag.trim()).filter(tag => tag)
          }
        }
      }
    })

    // 最终解析
    const lines = aiResponse.split('\n')
    for (const line of lines) {
      const trimmed = line.trim()
      if (trimmed.startsWith('外貌：')) {
        characterForm.value.appearance = trimmed.replace('外貌：', '').trim()
      } else if (trimmed.startsWith('性格：')) {
        characterForm.value.personality = trimmed.replace('性格：', '').trim()
      } else if (trimmed.startsWith('背景：')) {
        characterForm.value.background = trimmed.replace('背景：', '').trim()
      } else if (trimmed.startsWith('标签：')) {
        const tagString = trimmed.replace('标签：', '').trim()
        characterForm.value.tags = tagString.split(',').map(tag => tag.trim()).filter(tag => tag)
      }
    }

    ElMessage.success('AI角色生成完成')
  } catch (error) {
    console.error('AI生成角色失败:', error)
    ElMessage.error(`角色生成失败: ${error.message}`)
  } finally {
    isStreaming.value = false
    streamingContent.value = ''
  }
}

const addCharacterTag = () => {
  const tag = characterTagInput.value.trim()
  if (tag && !characterForm.value.tags.includes(tag)) {
    characterForm.value.tags.push(tag)
    characterTagInput.value = ''
  }
}

const removeCharacterTag = (index) => {
  characterForm.value.tags.splice(index, 1)
}

// 处理人物操作
const handleCharacterAction = (command, character) => {
  switch (command) {
    case 'edit':
      editCharacter(character)
      break
    case 'delete':
      deleteCharacter(character)
      break
  }
}

// 删除角色
const deleteCharacter = (character) => {
  ElMessageBox.confirm(`确定要删除角色《${character.name}》吗？`, '确认删除', {
    type: 'warning',
    confirmButtonText: '删除',
    cancelButtonText: '取消',
    confirmButtonClass: 'el-button--danger'
  }).then(async () => {
    if (!currentNovel.value?.id) {
      ElMessage.error('小说信息不存在')
      return
    }

    try {
      // 调用后端API删除角色
      await characterApi.deleteCharacter(currentNovel.value.id, character.id)

      // 从本地角色列表中移除
      const index = characters.value.findIndex(c => c.id === character.id)
      if (index > -1) {
        characters.value.splice(index, 1)
        ElMessage.success('角色已删除')
        saveNovelData() // 同时更新localStorage
      }
    } catch (error) {
      console.error('删除角色失败:', error)
      ElMessage.error('删除角色失败: ' + (error.message || '未知错误'))
    }
  }).catch(() => {
    // 用户取消删除
  })
}

const openPromptDialog = async (category) => {
  try {
    // 加载提示词数据
    await loadPrompts()
    showPromptDialog.value = true
  } catch (error) {
    console.error('加载提示词失败:', error)
    ElMessage.error('加载提示词失败: ' + error.message)
  }
}

const showBatchGenerateDialog = () => {
  showBatchGenerateCharacterDialog.value = true
  // 重置配置
  batchGenerateConfig.value = {
    count: 5,
    includeMainCharacters: true,
    includeSupportingCharacters: true,
    includeMinorCharacters: true,
    customPrompt: '',
    autoAssignRoles: true
  }
  generatedCharacters.value = []
  streamingContent.value = ''
}

// 加载人物数据
const loadCharacters = async () => {
  if (!currentNovel.value?.id) return

  try {
    charactersLoading.value = true
    // 从后端API加载人物数据
    const response = await characterApi.getCharacters(currentNovel.value.id)
    console.log('人物数据响应:', response)

    // 处理后端响应数据结构
    let charactersData = []
    if (response && response.data) {
      // 如果是标准的 Result 格式 {code, message, data}
      if (Array.isArray(response.data)) {
        charactersData = response.data
      } else {
        console.warn('response.data 不是数组格式:', response.data)
        charactersData = []
      }
    } else if (response && Array.isArray(response)) {
      // 直接数组格式
      charactersData = response
    } else {
      console.warn('未知的响应数据格式:', response)
      charactersData = []
    }

    characters.value = charactersData.map(character => ({
      ...character,
      createdAt: character.createdAt ? new Date(character.createdAt) : new Date(),
      updatedAt: character.updatedAt ? new Date(character.updatedAt) : new Date()
    }))

    console.log('从后端加载人物数据:', characters.value.length, '个人物')
  } catch (error) {
    console.error('加载人物数据失败:', error)
    // 如果后端加载失败，尝试从localStorage加载作为备用
    const localData = JSON.parse(localStorage.getItem('novels') || '[]')
    const localNovel = localData.find(n => n.id === currentNovel.value.id)
    characters.value = localNovel?.characters || []
    ElMessage.warning('人物数据加载失败，使用本地缓存数据')
  } finally {
    charactersLoading.value = false
  }
}

// 批量生成角色
const batchGenerateCharacters = async () => {
  if (!checkApiAndBalance()) return

  batchGenerating.value = true
  generatedCharacters.value = []
  streamingContent.value = ''

  try {
    // 获取用户配置
    const characterTypes = []
    if (batchGenerateConfig.value.includeMainCharacters) characterTypes.push('主角')
    if (batchGenerateConfig.value.includeSupportingCharacters) characterTypes.push('配角')
    if (batchGenerateConfig.value.includeMinorCharacters) characterTypes.push('次要角色')

    const finalPrompt = `=== 小说基本信息 ===
小说标题：${currentNovel.value?.title || '未命名小说'}
小说类型：${(() => {
      const genreMap = {
        'fantasy': '玄幻小说',
        'urban': '都市言情',
        'historical': '历史架空',
        'martial': '武侠修仙',
        'science': '科幻未来',
        'romance': '现代言情',
        'mystery': '悬疑推理',
        'adventure': '冒险奇幻',
        'horror': '恐怖惊悚',
        'general': '通用小说'
      }
      return genreMap[currentNovel.value?.genre] || '通用小说'
    })()}
小说简介：${currentNovel.value?.description || '暂无简介'}

=== 角色生成任务 ===
你是一个专业的小说角色生成器。请严格按照指定格式为上述小说生成${batchGenerateConfig.value.count}个人物角色。

【重要】必须严格按照以下格式输出，不要添加任何额外的解释或文字：

角色1：
姓名：张三
角色：主角
性别：男
年龄：25
外貌：身高一米八，浓眉大眼，面容坚毅
性格：勇敢正直，有些冲动，但心地善良
背景：出身农家，自幼习武，立志成为英雄
标签：主角,勇敢,正义

角色2：
姓名：李美娜
角色：配角
性别：女
年龄：22
外貌：身材娇小，长发飘逸，眼神清澈动人
性格：温柔善良，聪明机智，偶尔有些任性
背景：大家闺秀，从小接受良好教育，精通琴棋书画
标签：配角,温柔,才女

请完全按照以上示例格式生成${batchGenerateConfig.value.count}个角色，每个角色都必须包含：姓名、角色、性别、年龄、外貌、性格、背景、标签这8个字段。

=== 生成要求 ===
角色类型要求：${characterTypes.join('、')}
${batchGenerateConfig.value.customPrompt ? `特殊要求：${batchGenerateConfig.value.customPrompt}` : ''}

请确保所有角色设定都符合小说的世界观、类型和风格特点。

开始生成：`

    // 为批量角色生成添加强制格式后缀
    const formatSuffix = `

=== 重要格式要求 ===
无论上述提示词如何，你必须严格按照以下格式输出，不得有任何偏差：

请生成${batchGenerateConfig.value.count}个角色，角色类型包括：${characterTypes.join('、')}

角色1：
姓名：[角色姓名]
角色：[主角/配角/反派/次要角色]
性别：[男/女/其他]
年龄：[数字]
外貌：[详细外貌描述]
性格：[性格特点描述]
背景：[背景故事]
标签：[标签1,标签2,标签3]

角色2：
姓名：[角色姓名]
角色：[主角/配角/反派/次要角色]
性别：[男/女/其他]
年龄：[数字]
外貌：[详细外貌描述]
性格：[性格特点描述]
背景：[背景故事]
标签：[标签1,标签2,标签3]

继续按此格式直到生成完所有${batchGenerateConfig.value.count}个角色。每个角色必须包含这8个字段。角色类型应该在${characterTypes.join('、')}中选择。`

    const finalPromptWithFormat = finalPrompt + formatSuffix

    const aiResponse = await apiService.generateTextStream(finalPromptWithFormat, {
      maxTokens: null,
      temperature: 0.8,
      type: 'character'
    }, (chunk, fullContent) => {
      // 实时更新流式内容
      streamingContent.value = fullContent

      // 实时解析角色
      parseGeneratedCharacters(fullContent)

      // 自动滚动到最新内容
      nextTick(() => {
        const streamElement = document.querySelector('.streaming-content')
        if (streamElement) {
          streamElement.scrollTop = streamElement.scrollHeight
        }
      })
    })

    // 最终解析
    parseGeneratedCharacters(aiResponse)

    ElMessage.success(`成功生成 ${generatedCharacters.value.length} 个角色`)
  } catch (error) {
    console.error('批量生成角色失败:', error)
    ElMessage.error(`批量生成失败: ${error.message}`)
  } finally {
    batchGenerating.value = false
  }
}

// 解析生成的角色信息
const parseGeneratedCharacters = (content) => {
  if (!content || !content.trim()) {
    generatedCharacters.value = []
    return
  }

  // 通用的角色信息提取函数
  const extractCharacterInfo = (text) => {
    const character = {
      id: Date.now() + Math.random() * 1000,
      name: '',
      role: 'supporting',
      gender: 'male',
      age: 25,
      appearance: '',
      personality: '',
      background: '',
      tags: [],
      avatar: '',
      createdAt: new Date(),
      generated: true,
      selected: true // 默认选中
    }

    // 提取姓名
    const nameMatch = text.match(/(?:姓名|名字)\s*[：:]\s*([^\n\r]+)/i)
    if (nameMatch && nameMatch[1]) {
      character.name = nameMatch[1].trim()
    }

    // 提取角色类型
    const roleMatch = text.match(/(?:角色|职责|定位|类型)\s*[：:]\s*([^\n\r]+)/i)
    if (roleMatch && roleMatch[1]) {
      const roleText = roleMatch[1].trim()
      if (roleText.includes('主角')) {
        character.role = 'protagonist'
      } else if (roleText.includes('反派')) {
        character.role = 'antagonist'
      } else if (roleText.includes('配角')) {
        character.role = 'supporting'
      } else {
        character.role = 'minor'
      }
    }

    // 提取性别
    const genderMatch = text.match(/(?:性别)\s*[：:]\s*([^\n\r]+)/i)
    if (genderMatch && genderMatch[1]) {
      const genderText = genderMatch[1].trim().toLowerCase()
      if (genderText.includes('女')) {
        character.gender = 'female'
      } else if (genderText.includes('男')) {
        character.gender = 'male'
      } else {
        character.gender = 'other'
      }
    }

    // 提取年龄
    const ageMatch = text.match(/(?:年龄)\s*[：:]\s*(\d+)/i)
    if (ageMatch && ageMatch[1]) {
      const age = parseInt(ageMatch[1])
      if (!isNaN(age) && age > 0 && age < 200) {
        character.age = age
      }
    }

    // 提取外貌
    const appearanceMatch = text.match(/(?:外貌|外观|长相)\s*[：:]\s*([^\n\r姓名角色性别年龄性格背景标签]+)/i)
    if (appearanceMatch && appearanceMatch[1]) {
      character.appearance = appearanceMatch[1].trim()
    }

    // 提取性格
    const personalityMatch = text.match(/(?:性格|个性)\s*[：:]\s*([^\n\r姓名角色性别年龄外貌背景标签]+)/i)
    if (personalityMatch && personalityMatch[1]) {
      character.personality = personalityMatch[1].trim()
    }

    // 提取背景
    const backgroundMatch = text.match(/(?:背景|出身|来历)\s*[：:]\s*([^\n\r姓名角色性别年龄外貌性格标签]+)/i)
    if (backgroundMatch && backgroundMatch[1]) {
      character.background = backgroundMatch[1].trim()
    }

    // 提取标签
    const tagMatch = text.match(/(?:标签|tags?)\s*[：:]\s*([^\n\r]+)/i)
    if (tagMatch && tagMatch[1]) {
      const tagString = tagMatch[1].trim()
      character.tags = tagString.split(/[,，、\s]+/).map(tag => tag.trim()).filter(tag => tag && tag.length > 0)
    }

    return character
  }

  // 分割角色块
  let characterBlocks = []

  // 尝试按"角色X："分割
  if (content.includes('角色') && /角色\d+[：:]/g.test(content)) {
    characterBlocks = content.split(/(?=角色\d+[：:])/g).filter(block => block.trim())
  }
  // 尝试按姓名分割
  else if (content.includes('姓名')) {
    characterBlocks = content.split(/(?=姓名[：:])/g).filter(block => block.trim())
  }
  // 尝试按双换行分割
  else {
    characterBlocks = content.split(/\n\s*\n/).filter(block => block.trim())
  }

  const parsed = []

  // 处理每个角色块
  characterBlocks.forEach((block, index) => {
    if (!block.trim()) return

    const character = extractCharacterInfo(block)

    // 如果没有提取到姓名，生成默认姓名
    if (!character.name) {
      character.name = `角色${index + 1}`
    }

    // 为空字段提供默认值
    if (!character.appearance) {
      character.appearance = '外貌特征待补充'
    }
    if (!character.personality) {
      character.personality = '性格特点待补充'
    }
    if (!character.background) {
      character.background = '背景故事待补充'
    }
    if (character.tags.length === 0) {
      character.tags = [character.role === 'protagonist' ? '主角' : '配角']
    }

    // 只要有姓名就添加
    if (character.name && character.name !== '角色') {
      parsed.push(character)
    }
  })

  generatedCharacters.value = parsed
}

// 切换角色选择状态
const toggleCharacterSelection = (character) => {
  character.selected = character.selected !== false ? false : true
}

// 确认添加生成的角色
const confirmAddGeneratedCharacters = async () => {
  const selectedCharacters = generatedCharacters.value.filter(char => char.selected !== false)

  if (selectedCharacters.length === 0) {
    ElMessage.warning('请选择要添加的角色')
    return
  }

  if (!currentNovel.value?.id) {
    ElMessage.error('小说信息不存在')
    return
  }

  try {
    // 保存角色到后端并添加到本地列表
    const savedCharacters = []
    for (let i = 0; i < selectedCharacters.length; i++) {
      const character = selectedCharacters[i]
      try {
        const characterData = {
          name: character.name,
          role: character.role,
          gender: character.gender,
          age: character.age,
          appearance: character.appearance,
          personality: character.personality,
          background: character.background,
          tags: character.tags || [],
          avatar: character.avatar || '',
          generated: 1 // 标记为AI生成
        }

        const savedCharacter = await characterApi.createCharacter(currentNovel.value.id, characterData)

        // 添加到本地角色列表
        const newCharacter = {
          ...savedCharacter,
          createdAt: new Date(savedCharacter.createdAt),
          updatedAt: new Date(savedCharacter.updatedAt)
        }

        characters.value.push(newCharacter)
        savedCharacters.push(newCharacter)
      } catch (error) {
        console.error(`保存角色 ${character.name} 失败:`, error)
        ElMessage.error(`保存角色"${character.name}"失败: ${error.message}`)
      }
    }

    // 保存数据到localStorage作为备份
    saveNovelData()

    // 关闭对话框
    showBatchGenerateCharacterDialog.value = false

    if (savedCharacters.length > 0) {
      ElMessage.success(`成功添加 ${savedCharacters.length} 个角色`)
    }
  } catch (error) {
    console.error('批量保存角色失败:', error)
    ElMessage.error('批量保存角色失败: ' + (error.message || '未知错误'))
  }
}

// 关闭批量生成对话框
const closeBatchGenerateDialog = () => {
  showBatchGenerateCharacterDialog.value = false
  generatedCharacters.value = []
  streamingContent.value = ''
  batchGenerating.value = false
}

// 格式化流式内容
const formatStreamingContent = (content) => {
  if (!content) return ''
  // 将换行符转换为HTML换行
  let formatted = content.replace(/\n/g, '<br/>')

  // 高亮角色标题
  formatted = formatted.replace(/(角色\d+：)/g, '<strong style="color: #409eff; font-size: 16px;">$1</strong>')
  return formatted
}

// 提示词相关方法
const loadPrompts = async () => {
  try {
    const data = await promptApi.getPrompts('character')
    availablePrompts.value = data || []
    console.log('人物提示词加载完成，数量:', availablePrompts.value.length)
  } catch (error) {
    console.error('加载人物提示词失败:', error)
    // 如果API调用失败，使用空数组
    availablePrompts.value = []
    throw error
  }
}

// 处理提示词选择事件
const handlePromptSelected = (data) => {
  selectedPrompt.value = data.prompt
  promptVariables.value = data.variables
  finalPrompt.value = data.finalPrompt
}

// 使用选中的提示词
const useSelectedPrompt = (data) => {
  if (!data || !data.prompt || !data.finalPrompt) {
    ElMessage.warning('请选择提示词并填充变量')
    return
  }

  // 将提示词应用到当前角色生成
  showPromptDialog.value = false
  ElMessage.success('提示词已选择，可以开始生成角色')

  // 这里可以将提示词内容应用到角色生成逻辑中
  // 例如：将finalPrompt用于generateCharacterAI方法
}

// 复制提示词到剪贴板
const copyPromptToClipboard = async (promptText) => {
  try {
    await navigator.clipboard.writeText(promptText)
    ElMessage.success('提示词已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 跳转到提示词库
const goToPromptLibrary = () => {
  ElMessage.info('请前往提示词库添加人物生成提示词')
}

// 组件挂载时加载数据
onMounted(() => {
  loadCharacters()
})
</script>

<style scoped>
.character-management-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #fafafa;
}

.panel-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.panel-icon {
  margin-right: 8px;
  font-size: 18px;
}

.character-actions {
  display: flex;
  gap: 8px;
}

.characters-list {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.character-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
  background-color: #ffffff;
}

.character-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.character-content {
  display: flex;
  flex: 1;
  cursor: pointer;
}

.character-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 12px;
  flex-shrink: 0;
}

.character-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-avatar {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
}

.character-info {
  flex: 1;
}

.character-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.character-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.age-text {
  font-size: 12px;
  color: #909399;
}

.character-desc {
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
  margin-bottom: 8px;
}

.character-desc-truncated {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: help;
  transition: color 0.2s ease;
}

.character-desc-truncated:hover {
  color: #303133;
}

.character-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.character-tags .el-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}

.character-item .character-actions {
  flex-shrink: 0;
  margin-left: 12px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-state p {
  margin-bottom: 16px;
  font-size: 14px;
}

.form-item-with-ai {
  position: relative;
}

.ai-button-group {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

/* 对话框样式优化 */
:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid #e4e7ed;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-input-number) {
  width: 100%;
}

/* 标签样式 */
:deep(.el-tag) {
  border-radius: 4px;
}

/* 按钮样式 */
:deep(.el-button) {
  border-radius: 6px;
}

/* 下拉菜单样式 */
:deep(.el-dropdown-menu) {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
